export default function Home() {
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1e293b 0%, #7c3aed 50%, #1e293b 100%)',
      color: 'white',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'system-ui, sans-serif'
    }}>
      <div style={{ textAlign: 'center', maxWidth: '800px', padding: '20px' }}>
        <h1 style={{
          fontSize: '4rem',
          fontWeight: 'bold',
          marginBottom: '2rem',
          background: 'linear-gradient(135deg, #60a5fa, #a855f7)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}>
          ReferralAI
        </h1>
        <p style={{ fontSize: '1.5rem', marginBottom: '1rem', color: '#d1d5db' }}>
          The Future of Performance Marketing
        </p>
        <p style={{ fontSize: '1.2rem', marginBottom: '3rem', color: '#9ca3af' }}>
          AI-Enhanced Referral Platform - The Uber for Referrals. Transform your business with people-powered results.
        </p>
        <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
          <button style={{
            background: 'linear-gradient(135deg, #3b82f6, #8b5cf6)',
            color: 'white',
            padding: '1rem 2rem',
            borderRadius: '0.75rem',
            border: 'none',
            fontWeight: 'bold',
            fontSize: '1.1rem',
            cursor: 'pointer'
          }}>
            Start Building MVP
          </button>
          <button style={{
            background: 'transparent',
            color: 'white',
            padding: '1rem 2rem',
            borderRadius: '0.75rem',
            border: '2px solid rgba(255,255,255,0.2)',
            fontWeight: 'bold',
            fontSize: '1.1rem',
            cursor: 'pointer'
          }}>
            Watch Demo
          </button>
        </div>
      </div>
    </div>
  )
}
