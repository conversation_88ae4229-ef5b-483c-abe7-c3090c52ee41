import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'ReferralAI - The Future of Performance Marketing',
  description: 'AI-Enhanced Referral Platform - The Uber for Referrals. Transform your business with people-powered results.',
  keywords: 'referral platform, AI marketing, performance marketing, affiliate marketing, referral program',
  authors: [{ name: 'ReferralAI Team' }],
  openGraph: {
    title: 'ReferralAI - The Future of Performance Marketing',
    description: 'AI-Enhanced Referral Platform - The Uber for Referrals',
    type: 'website',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>{children}</body>
    </html>
  )
}
