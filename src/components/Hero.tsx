'use client'

import { motion } from 'framer-motion'
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>rk<PERSON>, Zap, TrendingUp } from 'lucide-react'

export default function Hero() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden pt-20">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-blue-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        {/* Badge */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-blue-500/30 rounded-full px-6 py-2 mb-8"
        >
          <Sparkles className="h-4 w-4 text-blue-400" />
          <span className="text-sm text-blue-300 font-medium">The Future of Performance Marketing</span>
          <Sparkles className="h-4 w-4 text-purple-400" />
        </motion.div>

        {/* Main Headline */}
        <motion.h1
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight"
        >
          The{' '}
          <span className="gradient-text">Uber for Referrals</span>
          <br />
          Powered by AI
        </motion.h1>

        {/* Subtitle */}
        <motion.p
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-xl md:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed"
        >
          Transform your business with our AI-enhanced referral platform where{' '}
          <span className="text-blue-400 font-semibold">anyone can earn money</span> by sharing real offers,{' '}
          <span className="text-purple-400 font-semibold">businesses only pay for results</span>, and{' '}
          <span className="text-green-400 font-semibold">customers get better deals</span> through trusted people.
        </motion.p>

        {/* Key Benefits */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="flex flex-wrap justify-center gap-6 mb-12"
        >
          {[
            { icon: Zap, text: 'AI-Powered Matching', color: 'text-yellow-400' },
            { icon: TrendingUp, text: 'Performance-Based', color: 'text-green-400' },
            { text: 'Stripe Connect Ready', color: 'text-blue-400' }
          ].map((item, index) => (
            <div key={index} className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
              {item.icon && <item.icon className={`h-5 w-5 ${item.color}`} />}
              <span className="text-white font-medium">{item.text}</span>
            </div>
          ))}
        </motion.div>

        {/* CTA Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16"
        >
          <motion.button
            whileHover={{ scale: 1.05, boxShadow: '0 20px 40px rgba(59, 130, 246, 0.4)' }}
            whileTap={{ scale: 0.95 }}
            className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-xl font-bold text-lg flex items-center space-x-2 shadow-2xl pulse-glow"
          >
            <span>Start Building MVP</span>
            <ArrowRight className="h-5 w-5" />
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="glass-effect text-white px-8 py-4 rounded-xl font-bold text-lg flex items-center space-x-2 hover:bg-white/20 transition-all duration-200"
          >
            <Play className="h-5 w-5" />
            <span>Watch Demo</span>
          </motion.button>
        </motion.div>

        {/* Stats Preview */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
        >
          {[
            { number: '4-6 Weeks', label: 'MVP Delivery', color: 'text-blue-400' },
            { number: '100%', label: 'Performance-Based', color: 'text-green-400' },
            { number: 'AI-Enhanced', label: 'Smart Matching', color: 'text-purple-400' }
          ].map((stat, index) => (
            <div key={index} className="text-center">
              <div className={`text-3xl md:text-4xl font-bold ${stat.color} mb-2`}>
                {stat.number}
              </div>
              <div className="text-gray-300 font-medium">{stat.label}</div>
            </div>
          ))}
        </motion.div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 floating-animation">
        <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full opacity-20"></div>
      </div>
      <div className="absolute bottom-20 right-10 floating-animation delay-1000">
        <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full opacity-20"></div>
      </div>
    </section>
  )
}
