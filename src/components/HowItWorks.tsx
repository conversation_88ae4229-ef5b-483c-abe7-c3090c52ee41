'use client'

import { motion } from 'framer-motion'
import { 
  UserPlus, 
  Target, 
  Share2, 
  DollarSign, 
  ArrowRight,
  Building2,
  Users,
  TrendingUp
} from 'lucide-react'

export default function HowItWorks() {
  const steps = [
    {
      icon: Building2,
      title: 'Businesses Create Offers',
      description: 'Companies set up performance-based deals with AI-suggested rewards and targeting',
      details: ['Define success metrics', 'Set reward amounts', 'AI optimization suggestions'],
      color: 'from-blue-500 to-cyan-600'
    },
    {
      icon: Users,
      title: 'Referrers Join & Share',
      description: 'Anyone can sign up, browse offers, and share through multiple channels',
      details: ['Browse AI-matched deals', 'Generate trackable links', 'Share via social media'],
      color: 'from-purple-500 to-pink-600'
    },
    {
      icon: Target,
      title: 'Customers Take Action',
      description: 'Referred customers complete desired actions like purchases or bookings',
      details: ['Click referral links', 'Complete purchases', 'Actions are tracked'],
      color: 'from-green-500 to-emerald-600'
    },
    {
      icon: DollarSign,
      title: 'Automated Payouts',
      description: 'Stripe Connect processes payments to businesses and referrers automatically',
      details: ['Instant verification', 'Automated payments', 'Commission distribution'],
      color: 'from-orange-500 to-red-600'
    }
  ]

  const userTypes = [
    {
      icon: Building2,
      title: 'For Businesses',
      subtitle: 'Performance-Based Growth',
      features: [
        'Only pay for confirmed results',
        'AI-optimized deal creation',
        'Real-time performance tracking',
        'Automated payout management',
        'Quality referrer matching'
      ],
      cta: 'Start Selling',
      color: 'from-blue-500 to-purple-600'
    },
    {
      icon: Users,
      title: 'For Referrers',
      subtitle: 'Earn by Sharing',
      features: [
        'Earn money sharing great deals',
        'AI suggests best opportunities',
        'Multiple sharing channels',
        'Gamified ranking system',
        'Instant payout processing'
      ],
      cta: 'Start Earning',
      color: 'from-green-500 to-blue-600'
    }
  ]

  return (
    <section id="how-it-works" className="py-20 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            How Our Platform
            <span className="gradient-text"> Works</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            A simple, automated process that benefits everyone in the ecosystem
          </p>
        </motion.div>

        {/* Process Steps */}
        <div className="mb-20">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {steps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="relative"
              >
                {/* Connection Line */}
                {index < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-gray-600 to-transparent z-0">
                    <motion.div
                      initial={{ scaleX: 0 }}
                      whileInView={{ scaleX: 1 }}
                      transition={{ duration: 0.8, delay: index * 0.2 + 0.5 }}
                      viewport={{ once: true }}
                      className={`h-full bg-gradient-to-r ${step.color} origin-left`}
                    />
                  </div>
                )}

                <div className="glass-effect rounded-2xl p-6 text-center relative z-10 hover:bg-white/20 transition-all duration-300 group">
                  {/* Step Number */}
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className={`w-8 h-8 rounded-full bg-gradient-to-r ${step.color} flex items-center justify-center text-white font-bold text-sm`}>
                      {index + 1}
                    </div>
                  </div>

                  {/* Icon */}
                  <div className={`inline-flex items-center justify-center w-16 h-16 rounded-xl bg-gradient-to-r ${step.color} mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <step.icon className="h-8 w-8 text-white" />
                  </div>

                  {/* Title */}
                  <h3 className="text-xl font-bold text-white mb-3">
                    {step.title}
                  </h3>

                  {/* Description */}
                  <p className="text-gray-300 mb-4 text-sm">
                    {step.description}
                  </p>

                  {/* Details */}
                  <ul className="space-y-1">
                    {step.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="text-xs text-gray-400 flex items-center">
                        <div className={`w-1.5 h-1.5 rounded-full bg-gradient-to-r ${step.color} mr-2`}></div>
                        {detail}
                      </li>
                    ))}
                  </ul>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* User Types */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {userTypes.map((userType, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: index === 0 ? -30 : 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              className="glass-effect rounded-2xl p-8 hover:bg-white/20 transition-all duration-300 group"
            >
              {/* Header */}
              <div className="flex items-center mb-6">
                <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${userType.color} flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300`}>
                  <userType.icon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white">{userType.title}</h3>
                  <p className="text-gray-300">{userType.subtitle}</p>
                </div>
              </div>

              {/* Features */}
              <ul className="space-y-3 mb-8">
                {userType.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center text-gray-300">
                    <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${userType.color} mr-3`}></div>
                    {feature}
                  </li>
                ))}
              </ul>

              {/* CTA */}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`w-full bg-gradient-to-r ${userType.color} text-white py-3 rounded-xl font-bold flex items-center justify-center space-x-2 hover:shadow-lg transition-all duration-200`}
              >
                <span>{userType.cta}</span>
                <ArrowRight className="h-4 w-4" />
              </motion.button>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
