'use client'

import { motion } from 'framer-motion'
import { 
  Brain, 
  Shield, 
  Zap, 
  Target, 
  Users, 
  TrendingUp, 
  DollarSign, 
  Globe,
  Smartphone,
  BarChart3,
  Settings,
  Award
} from 'lucide-react'

export default function Features() {
  const features = [
    {
      icon: Brain,
      title: 'AI-Powered Matching',
      description: 'Smart algorithms match the right referrers with the perfect offers, maximizing conversion rates and earnings.',
      color: 'from-purple-500 to-pink-600',
      benefits: ['Smart deal suggestions', 'Predictive analytics', 'Auto-optimization']
    },
    {
      icon: DollarSign,
      title: 'Stripe Connect Integration',
      description: 'Seamless payment processing with automated payouts, commission handling, and budget management.',
      color: 'from-green-500 to-emerald-600',
      benefits: ['Instant payouts', 'Global payments', 'Fraud protection']
    },
    {
      icon: Target,
      title: 'Performance-Based Results',
      description: 'Businesses only pay for confirmed actions like bookings, purchases, or redemptions - no wasted ad spend.',
      color: 'from-blue-500 to-cyan-600',
      benefits: ['Pay per result', 'ROI tracking', 'Budget controls']
    },
    {
      icon: Users,
      title: 'Gamified Experience',
      description: 'Rank systems, badges, and leaderboards motivate referrers to share quality offers and build their reputation.',
      color: 'from-orange-500 to-red-600',
      benefits: ['Ranking system', 'Achievement badges', 'Leaderboards']
    },
    {
      icon: Smartphone,
      title: 'Multi-Channel Tracking',
      description: 'Track referrals through links, QR codes, promo codes with advanced attribution and cookie-based logic.',
      color: 'from-indigo-500 to-purple-600',
      benefits: ['QR code sharing', 'Link tracking', 'Promo codes']
    },
    {
      icon: Shield,
      title: 'Trust & Security',
      description: 'Built-in dispute resolution, quality scoring, and fraud prevention to maintain platform integrity.',
      color: 'from-teal-500 to-green-600',
      benefits: ['Dispute handling', 'Quality scoring', 'Fraud detection']
    }
  ]

  return (
    <section id="features" className="py-20 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Everything You Need to
            <span className="gradient-text"> Scale Referrals</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Our comprehensive platform combines cutting-edge AI with proven referral marketing strategies
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -10 }}
              className="group relative"
            >
              <div className="glass-effect rounded-2xl p-8 h-full hover:bg-white/20 transition-all duration-300">
                {/* Icon */}
                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-xl bg-gradient-to-r ${feature.color} mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="h-8 w-8 text-white" />
                </div>

                {/* Title */}
                <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-blue-300 transition-colors duration-300">
                  {feature.title}
                </h3>

                {/* Description */}
                <p className="text-gray-300 mb-6 leading-relaxed">
                  {feature.description}
                </p>

                {/* Benefits */}
                <ul className="space-y-2">
                  {feature.benefits.map((benefit, benefitIndex) => (
                    <li key={benefitIndex} className="flex items-center text-sm text-gray-400">
                      <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${feature.color} mr-3`}></div>
                      {benefit}
                    </li>
                  ))}
                </ul>

                {/* Hover Gradient */}
                <div className={`absolute inset-0 rounded-2xl bg-gradient-to-r ${feature.color} opacity-0 group-hover:opacity-5 transition-opacity duration-300`}></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Tech Stack Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-20"
        >
          <div className="glass-effect rounded-2xl p-8 text-center">
            <h3 className="text-3xl font-bold text-white mb-6">
              Built with Modern Technology
            </h3>
            <p className="text-gray-300 mb-8 max-w-3xl mx-auto">
              Our platform leverages the latest technologies to deliver exceptional performance and scalability
            </p>
            
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
              {[
                'React/Next.js',
                'Supabase',
                'Stripe Connect',
                'OpenAI API',
                'TypeScript',
                'Tailwind CSS'
              ].map((tech, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.05 }}
                  className="bg-white/10 rounded-lg p-4 hover:bg-white/20 transition-all duration-200"
                >
                  <div className="text-white font-semibold text-sm">{tech}</div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
