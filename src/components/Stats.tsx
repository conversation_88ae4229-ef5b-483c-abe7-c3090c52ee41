'use client'

import { motion } from 'framer-motion'
import { TrendingUp, Users, DollarSign, Zap } from 'lucide-react'

export default function Stats() {
  const stats = [
    {
      icon: TrendingUp,
      number: '300%',
      label: 'Average ROI Increase',
      description: 'Businesses see 3x better returns with our AI-powered referral system',
      color: 'from-green-400 to-emerald-600'
    },
    {
      icon: Users,
      number: '10K+',
      label: 'Active Referrers',
      description: 'Growing community of people earning through quality referrals',
      color: 'from-blue-400 to-cyan-600'
    },
    {
      icon: DollarSign,
      number: '$2M+',
      label: 'Payouts Processed',
      description: 'Automated payments through Stripe Connect integration',
      color: 'from-purple-400 to-violet-600'
    },
    {
      icon: Zap,
      number: '24/7',
      label: 'AI Optimization',
      description: 'Continuous learning and improvement of referral matching',
      color: 'from-yellow-400 to-orange-600'
    }
  ]

  return (
    <section className="py-20 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Proven Results That
            <span className="gradient-text"> Drive Growth</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Our platform delivers measurable results for businesses and referrers alike
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -10, scale: 1.02 }}
              className="relative group"
            >
              <div className="glass-effect rounded-2xl p-8 text-center hover:bg-white/20 transition-all duration-300">
                {/* Icon */}
                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r ${stat.color} mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <stat.icon className="h-8 w-8 text-white" />
                </div>

                {/* Number */}
                <div className="text-4xl md:text-5xl font-bold text-white mb-2 group-hover:scale-105 transition-transform duration-300">
                  {stat.number}
                </div>

                {/* Label */}
                <div className="text-xl font-semibold text-gray-200 mb-3">
                  {stat.label}
                </div>

                {/* Description */}
                <div className="text-gray-400 text-sm leading-relaxed">
                  {stat.description}
                </div>

                {/* Hover Effect */}
                <div className={`absolute inset-0 rounded-2xl bg-gradient-to-r ${stat.color} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="glass-effect rounded-2xl p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
              Ready to Transform Your Business?
            </h3>
            <p className="text-gray-300 mb-6">
              Join the revolution of performance-based marketing with AI-powered referrals
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-3 rounded-xl font-bold text-lg hover:shadow-lg transition-all duration-200"
            >
              Get Started Today
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
