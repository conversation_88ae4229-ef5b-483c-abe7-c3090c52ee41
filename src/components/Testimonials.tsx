'use client'

import { motion } from 'framer-motion'
import { Star, Quote } from 'lucide-react'

export default function Testimonials() {
  const testimonials = [
    {
      name: '<PERSON>',
      role: 'Marketing Director',
      company: 'TechFlow Solutions',
      avatar: '👩‍💼',
      rating: 5,
      quote: 'ReferralAI transformed our customer acquisition. We\'re seeing 300% better ROI compared to traditional advertising, and only paying for actual results.',
      metrics: '300% ROI increase'
    },
    {
      name: '<PERSON>',
      role: 'E-commerce Founder',
      company: 'Urban Lifestyle',
      avatar: '👨‍💻',
      rating: 5,
      quote: 'The AI matching is incredible. It connects our products with the right referrers who actually understand our brand. Sales have doubled in 3 months.',
      metrics: '200% sales growth'
    },
    {
      name: '<PERSON>',
      role: 'Content Creator',
      company: 'Independent Referrer',
      avatar: '👩‍🎨',
      rating: 5,
      quote: 'I\'ve earned over $5,000 in my first month just by sharing deals I genuinely love. The AI suggestions are spot-on for my audience.',
      metrics: '$5K+ monthly earnings'
    },
    {
      name: '<PERSON>',
      role: 'Restaurant Owner',
      company: 'Seoul Kitchen',
      avatar: '👨‍🍳',
      rating: 5,
      quote: 'Finally, a marketing solution that works! We only pay when customers actually book and dine. Our referral program runs itself now.',
      metrics: '150% booking increase'
    },
    {
      name: '<PERSON>',
      role: 'Fitness Influencer',
      company: '@FitWithLisa',
      avatar: '💪',
      rating: 5,
      quote: 'The gamification keeps me motivated to share quality content. I\'ve reached Gold status and my earnings keep growing every month.',
      metrics: 'Gold tier referrer'
    },
    {
      name: 'Alex Johnson',
      role: 'SaaS Founder',
      company: 'CloudSync Pro',
      avatar: '👨‍💼',
      rating: 5,
      quote: 'The Stripe Connect integration is seamless. Payments are automated, and we can track every conversion. It\'s like having a sales team that works 24/7.',
      metrics: '24/7 automated sales'
    }
  ]

  return (
    <section className="py-20 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Loved by Businesses &
            <span className="gradient-text"> Referrers</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            See how our platform is transforming the way people earn and businesses grow
          </p>
        </motion.div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -10 }}
              className="group relative"
            >
              <div className="glass-effect rounded-2xl p-6 h-full hover:bg-white/20 transition-all duration-300 relative">
                {/* Quote Icon */}
                <div className="absolute top-4 right-4 opacity-20">
                  <Quote className="h-8 w-8 text-white" />
                </div>

                {/* Rating */}
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                  ))}
                </div>

                {/* Quote */}
                <blockquote className="text-gray-300 mb-6 leading-relaxed">
                  "{testimonial.quote}"
                </blockquote>

                {/* Metrics Badge */}
                <div className="inline-block bg-gradient-to-r from-green-500/20 to-blue-500/20 border border-green-500/30 rounded-full px-3 py-1 mb-4">
                  <span className="text-green-400 text-sm font-semibold">{testimonial.metrics}</span>
                </div>

                {/* Author */}
                <div className="flex items-center">
                  <div className="text-3xl mr-3">{testimonial.avatar}</div>
                  <div>
                    <div className="font-semibold text-white">{testimonial.name}</div>
                    <div className="text-sm text-gray-400">{testimonial.role}</div>
                    <div className="text-sm text-blue-400">{testimonial.company}</div>
                  </div>
                </div>

                {/* Hover Gradient */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Social Proof Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-16"
        >
          <div className="glass-effect rounded-2xl p-8 text-center">
            <h3 className="text-2xl font-bold text-white mb-6">
              Join Thousands of Satisfied Users
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {[
                { number: '4.9/5', label: 'Average Rating' },
                { number: '10,000+', label: 'Active Users' },
                { number: '500+', label: 'Businesses' },
                { number: '99.9%', label: 'Uptime' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl font-bold gradient-text mb-2">{stat.number}</div>
                  <div className="text-gray-300">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
