'use client'

import { motion } from 'framer-motion'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Clock, CheckCircle, Sparkles } from 'lucide-react'

export default function CTA() {
  const benefits = [
    'Full MVP in 4-6 weeks',
    'Stripe Connect integration',
    'AI-powered matching system',
    'Complete admin dashboard',
    'Mobile-responsive design',
    'Post-launch support included'
  ]

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          {/* Main CTA Section */}
          <div className="glass-effect rounded-3xl p-12 mb-12 relative overflow-hidden">
            {/* Floating Elements */}
            <div className="absolute top-6 left-6 floating-animation">
              <Sparkles className="h-6 w-6 text-blue-400 opacity-60" />
            </div>
            <div className="absolute top-6 right-6 floating-animation delay-1000">
              <Zap className="h-6 w-6 text-purple-400 opacity-60" />
            </div>

            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="inline-flex items-center space-x-2 bg-gradient-to-r from-green-500/20 to-blue-500/20 border border-green-500/30 rounded-full px-6 py-2 mb-6"
            >
              <Clock className="h-4 w-4 text-green-400" />
              <span className="text-green-400 font-semibold">Limited Time Offer</span>
            </motion.div>

            {/* Headline */}
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
              className="text-4xl md:text-6xl font-bold text-white mb-6"
            >
              Ready to Build the
              <span className="gradient-text"> Future of Referrals?</span>
            </motion.h2>

            {/* Subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto"
            >
              Let's create your AI-enhanced referral platform that will revolutionize how your business grows. 
              Full MVP delivery in just 4-6 weeks with cutting-edge technology.
            </motion.p>

            {/* Benefits Grid */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              viewport={{ once: true }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-10 max-w-4xl mx-auto"
            >
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center space-x-3 bg-white/5 rounded-lg p-3">
                  <CheckCircle className="h-5 w-5 text-green-400 flex-shrink-0" />
                  <span className="text-white font-medium">{benefit}</span>
                </div>
              ))}
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              viewport={{ once: true }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <motion.button
                whileHover={{ scale: 1.05, boxShadow: '0 20px 40px rgba(59, 130, 246, 0.4)' }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-10 py-4 rounded-xl font-bold text-lg flex items-center space-x-2 shadow-2xl pulse-glow"
              >
                <span>Start Your Project Now</span>
                <ArrowRight className="h-5 w-5" />
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="glass-effect text-white px-10 py-4 rounded-xl font-bold text-lg hover:bg-white/20 transition-all duration-200"
              >
                Schedule Consultation
              </motion.button>
            </motion.div>

            {/* Trust Indicators */}
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.7 }}
              viewport={{ once: true }}
              className="mt-8 flex flex-wrap justify-center items-center gap-6 text-sm text-gray-400"
            >
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span>No upfront payment</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span>Money-back guarantee</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span>24/7 support</span>
              </div>
            </motion.div>
          </div>

          {/* Secondary CTA */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 gap-8"
          >
            <div className="glass-effect rounded-2xl p-8 text-left">
              <h3 className="text-2xl font-bold text-white mb-4">For Businesses</h3>
              <p className="text-gray-300 mb-6">
                Transform your customer acquisition with performance-based referrals. Only pay for results.
              </p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-blue-500 to-cyan-600 text-white px-6 py-3 rounded-lg font-semibold flex items-center space-x-2"
              >
                <span>Get Business Demo</span>
                <ArrowRight className="h-4 w-4" />
              </motion.button>
            </div>

            <div className="glass-effect rounded-2xl p-8 text-left">
              <h3 className="text-2xl font-bold text-white mb-4">For Developers</h3>
              <p className="text-gray-300 mb-6">
                Join our team and help build the future of referral marketing with cutting-edge AI technology.
              </p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-purple-500 to-pink-600 text-white px-6 py-3 rounded-lg font-semibold flex items-center space-x-2"
              >
                <span>View Opportunities</span>
                <ArrowRight className="h-4 w-4" />
              </motion.button>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
